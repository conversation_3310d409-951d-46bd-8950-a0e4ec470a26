#ifndef MANUALRECOGNITIONWINDOW_H
#define MANUALRECOGNITIONWINDOW_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QTextEdit>
#include <QFileDialog>
#include <QMessageBox>
#include <QFileInfo>

// 前向声明
class QRCodeDetector;

/**
 * @brief 手工识别窗口类
 * 提供手动输入图片路径进行二维码识别的功能
 */
class ManualRecognitionWindow : public QDialog
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param qrDetector 二维码检测器指针
     * @param parent 父窗口
     */
    explicit ManualRecognitionWindow(QRCodeDetector* qrDetector, QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~ManualRecognitionWindow();

private slots:
    /**
     * @brief 浏览按钮点击槽函数
     */
    void onBrowseButtonClicked();
    
    /**
     * @brief 识别按钮点击槽函数
     */
    void onRecognizeButtonClicked();

private:
    /**
     * @brief 设置UI界面
     */
    void setupUI();
    
    /**
     * @brief 验证输入路径
     * @return 路径是否有效
     */
    bool validateInput();

private:
    QRCodeDetector* m_qrDetector;      // 二维码检测器
    QLineEdit* m_pathLineEdit;         // 图片路径输入框
    QPushButton* m_browseButton;       // 浏览按钮
    QPushButton* m_recognizeButton;    // 识别按钮
    QTextEdit* m_resultTextEdit;       // 结果显示区域
};

#endif // MANUALRECOGNITIONWINDOW_H
