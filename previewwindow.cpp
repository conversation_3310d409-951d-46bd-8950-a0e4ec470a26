#include "previewwindow.h"
#include "mainwindow.h"
#include "manualrecognitionwindow.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QMessageBox>
#include <QDebug>
#include <QDateTime>
#include <QCoreApplication>
#include <QDir>

#ifdef _WIN32
#include <windows.h>
#endif

PreviewWindow::PreviewWindow(const QString& deviceId, const QString& deviceName, QWidget *parent)
    : QWidget(parent)
    , m_deviceId(deviceId)
    , m_deviceName(deviceName)
    , m_previewWidget(nullptr)
    , m_statusLabel(nullptr)
    , m_infoLabel(nullptr)
    // , m_captureBtn(nullptr)
    // , m_recordBtn(nullptr)
    // , m_volumeSlider(nullptr)
    // , m_streamTypeCombo(nullptr)
    // , m_ptzUpBtn(nullptr)
    // , m_ptzDownBtn(nullptr)
    // , m_ptzLeftBtn(nullptr)
    // , m_ptzRightBtn(nullptr)
    // , m_ptzLeftUpBtn(nullptr)
    // , m_ptzLeftDownBtn(nullptr)
    // , m_ptzRightUpBtn(nullptr)
    // , m_ptzRightDownBtn(nullptr)
    // , m_ptzResetBtn(nullptr)
    // , m_irisSmallBtn(nullptr)
    // , m_irisLargeBtn(nullptr)
    // , m_focusNearBtn(nullptr)
    // , m_focusFarBtn(nullptr)
    // , m_zoomWideBtn(nullptr)
    // , m_zoomTileBtn(nullptr)
    , m_hUser(-1)
    , m_playHandle(-1)
    , m_isRecording(false)
    , m_isPreviewActive(false)
    , m_manualRecognitionBtn(nullptr)
    , m_manualRecognitionWindow(nullptr)
{
    qDebug() << "PreviewWindow constructor called for device:" << deviceId;
    setWindowTitle(QString("实时预览 - %1").arg(deviceId));
    setMinimumSize(400, 300);  // 使用更小的尺寸进行测试

    qDebug() << "Calling setupUI()";
    setupUI();

    // 连接信号槽，确保UI更新在主线程中执行
    connect(this, &PreviewWindow::captureResultReady,
            this, &PreviewWindow::onCaptureResultReceived,
            Qt::QueuedConnection);

    qDebug() << "PreviewWindow constructor completed";
}

PreviewWindow::~PreviewWindow()
{
    qDebug() << "PreviewWindow destructor called for device:" << m_deviceId;

    // 停止预览并清理资源
    stopRealPreview();
    stopPreview();

    // 清理手工识别窗口
    if (m_manualRecognitionWindow) {
        m_manualRecognitionWindow->close();
        delete m_manualRecognitionWindow;
        m_manualRecognitionWindow = nullptr;
    }

    qDebug() << "PreviewWindow destructor completed";
}

void PreviewWindow::setupUI()
{
    qDebug() << "setupUI() started";

    QVBoxLayout* mainLayout = new QVBoxLayout(this);

    // 顶部工具栏
    QHBoxLayout* toolbarLayout = new QHBoxLayout;

    QPushButton* backBtn = new QPushButton("← 返回");
    backBtn->setMaximumWidth(80);
    connect(backBtn, &QPushButton::clicked, this, &PreviewWindow::onBackButtonClicked);

    m_statusLabel = new QLabel("准备就绪");
    m_statusLabel->setStyleSheet("color: green; font-weight: bold;");

    // 手工识别按钮
    m_manualRecognitionBtn = new QPushButton("手工识别");
    m_manualRecognitionBtn->setMaximumWidth(80);
    m_manualRecognitionBtn->setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; } "
                                        "QPushButton:hover { background-color: #1976D2; }");
    connect(m_manualRecognitionBtn, &QPushButton::clicked, this, &PreviewWindow::onManualRecognitionButtonClicked);

    toolbarLayout->addWidget(backBtn);
    toolbarLayout->addWidget(m_statusLabel);
    toolbarLayout->addStretch();
    toolbarLayout->addWidget(m_manualRecognitionBtn);

    // 预览区域 - 创建专门的视频显示Widget
    m_previewWidget = new QWidget;
    m_previewWidget->setStyleSheet("background-color: black; border: 1px solid #ccc;");
    m_previewWidget->setMinimumHeight(400);
    m_previewWidget->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    // 在预览区域显示提示信息（当没有视频时显示）
    m_infoLabel = new QLabel(QString("设备预览区域\n\n设备ID: %1\n\n点击[开始预览]按钮开始视频预览").arg(m_deviceId));
    m_infoLabel->setAlignment(Qt::AlignCenter);
    m_infoLabel->setStyleSheet("color: white; font-size: 14px;");

    QVBoxLayout* previewLayout = new QVBoxLayout(m_previewWidget);
    previewLayout->addWidget(m_infoLabel);

    // 控制面板
    QWidget* controlPanel = new QWidget;
    QHBoxLayout* controlLayout = new QHBoxLayout(controlPanel);

    QPushButton* captureBtn = new QPushButton("拍照");
    // QPushButton* recordBtn = new QPushButton("开始录像");  // 暂时注释掉录像按钮
    QPushButton* startPreviewBtn = new QPushButton("开始预览");
    QPushButton* stopPreviewBtn = new QPushButton("停止预览");

    captureBtn->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px; border-radius: 4px; }");
    // recordBtn->setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 8px; border-radius: 4px; }");  // 录像按钮样式注释
    startPreviewBtn->setStyleSheet("QPushButton { background-color: #2196F3; color: white; padding: 8px; border-radius: 4px; }");
    stopPreviewBtn->setStyleSheet("QPushButton { background-color: #FF9800; color: white; padding: 8px; border-radius: 4px; }");

    connect(captureBtn, &QPushButton::clicked, this, &PreviewWindow::onCaptureButtonClicked);
    // connect(recordBtn, &QPushButton::clicked, this, &PreviewWindow::onRecordButtonClicked);  // 录像按钮连接注释
    connect(startPreviewBtn, &QPushButton::clicked, this, [this]() {
        qDebug() << "Start preview button clicked";
        if (startRealPreview()) {
            QMessageBox::information(this, "开始预览", "预览已开始");
        } else {
            QMessageBox::warning(this, "错误", "预览开始失败");
        }
    });
    connect(stopPreviewBtn, &QPushButton::clicked, this, [this]() {
        qDebug() << "Stop preview button clicked";
        stopRealPreview();
        QMessageBox::information(this, "停止预览", "预览已停止");
    });

    controlLayout->addWidget(startPreviewBtn);
    controlLayout->addWidget(stopPreviewBtn);
    controlLayout->addWidget(captureBtn);
    // controlLayout->addWidget(recordBtn);  // 录像按钮暂时不添加到布局
    controlLayout->addStretch();

    mainLayout->addLayout(toolbarLayout);
    mainLayout->addWidget(m_previewWidget, 1);  // 使用成员变量
    mainLayout->addWidget(controlPanel, 0);

    qDebug() << "setupUI() completed successfully";
}

bool PreviewWindow::startPreview()
{
    qDebug() << "startPreview() called";
    return true;
}

void PreviewWindow::stopPreview()
{
    qDebug() << "stopPreview() called";
}

bool PreviewWindow::captureImage()
{
    QMessageBox::information(this, "提示", "拍照功能开发中...");
    return true;
}

bool PreviewWindow::startRecord()
{
    QMessageBox::information(this, "提示", "录像功能开发中...");
    return true;
}

bool PreviewWindow::stopRecord()
{
    return true;
}

void PreviewWindow::setVolume(int volume)
{
    // 暂时不实现
}

void PreviewWindow::ptzControl(const QString& command, bool stop, int step)
{
    // 暂时不实现
}

// 槽函数实现
void PreviewWindow::onBackButtonClicked()
{
    qDebug() << "Back button clicked, closing preview window";
    close();
}

void PreviewWindow::onCaptureButtonClicked()
{
    qDebug() << "Capture button clicked";

    if (m_playHandle <= 0) {
        QMessageBox::warning(this, "拍照失败", "请先开始预览才能拍照");
        return;
    }

    // 使用CaptureManager执行手动抓图
    bool success = CaptureManager::performCapture(m_playHandle, m_deviceId, m_deviceName, CaptureType::Manual);

    if (success) {
        // 更新状态标签显示正在处理
        if (m_statusLabel) {
            m_statusLabel->setText("正在拍照...");
            m_statusLabel->setStyleSheet("color: blue; font-weight: bold;");
        }
        qDebug() << "Manual capture request sent successfully";
    } else {
        QMessageBox::warning(this, "拍照失败", "拍照请求发送失败");
        qDebug() << "Failed to send manual capture request";
    }
}

void PreviewWindow::onManualRecognitionButtonClicked()
{
    qDebug() << "Manual recognition button clicked";

    // 获取MainWindow以获取QRCodeDetector
    MainWindow* mainWindow = qobject_cast<MainWindow*>(parent());
    if (!mainWindow) {
        QMessageBox::warning(this, "错误", "无法获取主窗口引用");
        return;
    }

    QRCodeDetector* detector = mainWindow->getQRCodeDetector();
    if (!detector) {
        QMessageBox::warning(this, "错误", "二维码检测器未初始化");
        return;
    }

    // 创建或显示手工识别窗口
    if (!m_manualRecognitionWindow) {
        m_manualRecognitionWindow = new ManualRecognitionWindow(detector, this);
    }

    m_manualRecognitionWindow->show();
    m_manualRecognitionWindow->raise();
    m_manualRecognitionWindow->activateWindow();
}

// 录像按钮槽函数暂时注释掉
/*
void PreviewWindow::onRecordButtonClicked()
{
    qDebug() << "Record button clicked";
    if (m_isRecording) {
        QMessageBox::information(this, "停止录像", "录像已停止（模拟）");
        m_isRecording = false;
    } else {
        QMessageBox::information(this, "开始录像", "录像已开始（模拟）");
        m_isRecording = true;
    }
}
*/

void PreviewWindow::onVolumeChanged(int volume)
{
    setVolume(volume);
}

void PreviewWindow::onStreamTypeChanged(int index)
{
    // 暂时不实现
}

void PreviewWindow::onPtzButtonPressed()
{
    // 暂时不实现
}

void PreviewWindow::onPtzButtonReleased()
{
    // 暂时不实现
}

void PreviewWindow::setupPTZControls()
{
    // 暂时不实现
}

QWidget* PreviewWindow::createControlPanel()
{
    return new QWidget();
}

// SDK回调函数实现 - 参考demo
int CALLBACK PreviewWindow::previewCallback(XSDK_HANDLE hObject, int nMsgId, int nParam1, int nParam2,
                                           int nParam3, const char* szString, void* pObject,
                                           int64 lParam, int nSeq, void* pUserData, void* pMsg)
{
    PreviewWindow* previewWindow = static_cast<PreviewWindow*>(pUserData);
    if (previewWindow) {
        previewWindow->handleSDKMessage(hObject, nMsgId, nParam1, nParam2, nParam3, szString, pObject, lParam, nSeq);
    }
    return 0;
}

void PreviewWindow::handleSDKMessage(XSDK_HANDLE hObject, int nMsgId, int nParam1, int nParam2,
                                   int nParam3, const char* szString, void* pObject,
                                   int64 lParam, int nSeq)
{
    qDebug() << "Preview SDK Message - ID:" << nMsgId << "Param1:" << nParam1 << "Param2:" << nParam2 << "String:" << (szString ? szString : "null");

    // 参考demo处理预览相关消息
    switch (nMsgId) {
    case 30001: // ESXSDK_MEDIA_START_REAL_PLAY - 实时预览请求结果
        if (nParam1 >= 0) {
            qDebug() << "RealPlay Success";
        } else {
            qDebug() << "RealPlay Failed, error:" << nParam1;
        }
        break;
    case 30007: // EUIMSG_PLAY_SAVE_IMAGE_FILE - 预览抓图结果
        handleCaptureResult(nParam1, szString);
        break;
    case 30008: // EUIMSG_RECORD_START - 预览录像请求结果
        if (nParam1 >= 0) {
            qDebug() << "StartRecord Success";
        } else {
            qDebug() << "StartRecord Failed:" << nParam1;
        }
        break;
    case 30006: { // 30006是码流信息回调ID
        // 预览YUV格式数据返回
        int nDataLen = nParam1;
        unsigned char* YuvData = (unsigned char*)pObject;
        qDebug() << "Yuv data len: " << nDataLen;
    } break;
    default:
        qDebug() << "Unhandled preview message:" << nMsgId;
        break;
    }
}

// 实现真正的预览功能，参考demo
bool PreviewWindow::startRealPreview()
{
    qDebug() << "Starting real preview for device:" << m_deviceId;

    // 停止之前的预览
    stopRealPreview();

    // 注册预览回调函数
    m_hUser = XCloudSDK_RegisterCallback((PXSDK_MessageCallBack)previewCallback, this);
    if (m_hUser < 0) {
        qWarning() << "Failed to register preview callback, error:" << m_hUser;
        return false;
    }
    qDebug() << "Preview callback registered, handle:" << m_hUser;

    // 确保预览Widget存在且可见
    if (!m_previewWidget || !m_previewWidget->isVisible()) {
        qWarning() << "Preview widget is not available or visible";
        XCloudSDK_UnRegister(m_hUser);
        m_hUser = -1;
        return false;
    }

    // 隐藏提示信息，为视频显示让出空间
    if (m_infoLabel) {
        m_infoLabel->hide();
    }

    // 调用实时预览API，参考demo
    int nChannel = 0;  // 通道号，通常为0
    int nStreamType = 0;  // 码流类型：0-主码流，1-子码流
    std::string sRequestJson = "";  // 请求参数，暂时为空

    qDebug() << "Calling XCloudSDK_Device_MediaRealPlay...";
    qDebug() << "Device:" << m_deviceId << "Channel:" << nChannel << "StreamType:" << nStreamType;
    qDebug() << "Preview widget handle (HWND):" << (HWND)m_previewWidget->winId();

    m_playHandle = XCloudSDK_Device_MediaRealPlay(
        m_hUser,                           // 回调句柄
        m_deviceId.toUtf8().constData(),   // 设备ID
        nChannel,                          // 通道号
        nStreamType,                       // 码流类型
        (HWND)m_previewWidget->winId(),    // 使用预览Widget的窗口句柄
        0,                                 // 播放类型
        sRequestJson.c_str()               // 请求参数
    );

    qDebug() << "XCloudSDK_Device_MediaRealPlay result:" << m_playHandle;

    if (m_playHandle > 0) {
        m_isPreviewActive = true;

        // 隐藏提示标签，让视频画面显示
        if (m_infoLabel) {
            m_infoLabel->hide();
        }

        // 更新状态标签
        if (m_statusLabel) {
            m_statusLabel->setText("预览中");
            m_statusLabel->setStyleSheet("color: green; font-weight: bold;");
        }
        qDebug() << "Real preview started successfully, handle:" << m_playHandle;
        return true;
    } else {
        qWarning() << "Failed to start real preview, error:" << m_playHandle;
        // 更新状态标签
        if (m_statusLabel) {
            m_statusLabel->setText("预览失败");
            m_statusLabel->setStyleSheet("color: red; font-weight: bold;");
        }
        XCloudSDK_UnRegister(m_hUser);
        m_hUser = -1;
        return false;
    }
}

void PreviewWindow::stopRealPreview()
{
    qDebug() << "Stopping real preview";

    if (m_playHandle > 0) {
        qDebug() << "Stopping media play, handle:" << m_playHandle;
        XCloudSDK_Device_StopMediaPlay(m_playHandle);
        m_playHandle = -1;
    }

    if (m_hUser >= 0) {
        qDebug() << "Unregistering preview callback, handle:" << m_hUser;
        XCloudSDK_UnRegister(m_hUser);
        m_hUser = -1;
    }

    // 清除预览画面 - 强制重绘预览Widget
    if (m_previewWidget) {
        // 重新设置背景色来清除残留画面
        m_previewWidget->setStyleSheet("background-color: black; border: 1px solid #ccc;");

        // 强制清除Widget内容的多种方法
        m_previewWidget->repaint();
        m_previewWidget->update();

        // 尝试清除Widget的绘制缓存
        m_previewWidget->setAttribute(Qt::WA_OpaquePaintEvent, false);
        m_previewWidget->setAttribute(Qt::WA_NoSystemBackground, false);

        // 强制重新创建Widget的绘制表面
        m_previewWidget->hide();
        m_previewWidget->show();

        // 再次强制重绘
        QCoreApplication::processEvents();
        m_previewWidget->repaint();

#ifdef _WIN32
        // Windows特定的清理：清除窗口的绘制区域
        if (m_previewWidget->winId()) {
            HWND hwnd = (HWND)m_previewWidget->winId();
            // 使窗口区域无效，强制重绘
            InvalidateRect(hwnd, NULL, TRUE);
            // 立即重绘窗口
            UpdateWindow(hwnd);
            // 清除窗口背景
            HDC hdc = GetDC(hwnd);
            if (hdc) {
                RECT rect;
                GetClientRect(hwnd, &rect);
                FillRect(hdc, &rect, (HBRUSH)GetStockObject(BLACK_BRUSH));
                ReleaseDC(hwnd, hdc);
            }
        }
#endif
    }

    // 恢复提示信息的显示
    if (m_infoLabel) {
        m_infoLabel->show();
        m_infoLabel->setText(QString("设备预览区域\n\n设备ID: %1\n\n预览已停止\n点击[开始预览]按钮重新开始").arg(m_deviceId));
        m_infoLabel->raise(); // 确保标签显示在最前面
    }

    // 更新状态标签
    if (m_statusLabel) {
        m_statusLabel->setText("预览已停止");
        m_statusLabel->setStyleSheet("color: orange; font-weight: bold;");
    }

    m_isPreviewActive = false;
    qDebug() << "Real preview stopped";
}

// 处理抓图结果的回调（在SDK回调线程中执行）
void PreviewWindow::handleCaptureResult(int result, const char* filePath)
{
    qDebug() << "Capture result:" << result << "File:" << (filePath ? filePath : "null");

    // 将结果通过信号发送到主线程处理
    QString pathStr = QString::fromUtf8(filePath ? filePath : "");
    emit captureResultReady(result, pathStr);
}

// 在主线程中处理抓图结果（线程安全）
void PreviewWindow::onCaptureResultReceived(int result, const QString& filePath)
{
    qDebug() << "Processing capture result in main thread:" << result << "File:" << filePath;

    if (result >= 0) {
        // 抓图成功，使用CaptureManager处理文件移动
        QString finalPath = CaptureManager::handleCaptureComplete(filePath);

        if (!finalPath.isEmpty() && QFile::exists(finalPath)) {
            qDebug() << "Capture file successfully moved to:" << finalPath;

            // 进行二维码识别
            qDebug() << "PreviewWindow: Attempting to get MainWindow parent";
            qDebug() << "Parent widget:" << parent();
            MainWindow* mainWindow = qobject_cast<MainWindow*>(parent());
            qDebug() << "MainWindow cast result:" << mainWindow;

            QRCodeDetector* detector = mainWindow ? mainWindow->getQRCodeDetector() : nullptr;
            qDebug() << "QRCodeDetector:" << detector;

            // 初始化状态文本和样式
            QString statusText = "拍照成功";
            QString statusStyle = "color: green; font-weight: bold;";

            if (detector && detector->isInitialized()) {
                qDebug() << "Starting QR code detection on manual capture:" << finalPath;
                QStringList qrResults = detector->detectQRCodes(finalPath);

                if (!qrResults.isEmpty()) {
                    qDebug() << "=== QR Code Detection Results (Manual Capture) ===";
                    qDebug() << "Device ID:" << m_deviceId;
                    qDebug() << "Device Name:" << m_deviceName;
                    qDebug() << "Image Path:" << finalPath;
                    qDebug() << "Detected" << qrResults.size() << "QR codes:";

                    // 构建QR码检测成功的状态文本
                    statusText += " | QR码识别成功: ";
                    for (int i = 0; i < qrResults.size(); ++i) {
                        qDebug() << "QR Code" << (i + 1) << ":" << qrResults[i];
                        if (i > 0) statusText += ", ";
                        statusText += qrResults[i];
                    }
                    statusStyle = "color: blue; font-weight: bold;"; // 成功识别用蓝色
                    qDebug() << "=== QR Code Detection Completed ===";
                } else {
                    qDebug() << "No QR codes detected in manual capture:" << finalPath;
                    statusText += " | QR码识别失败";
                    statusStyle = "color: orange; font-weight: bold;"; // 识别失败用橙色
                    if (!detector->getLastError().isEmpty()) {
                        qWarning() << "QR code detection error:" << detector->getLastError();
                    }
                }
            } else {
                qWarning() << "QR code detector not initialized, skipping QR code detection";
                statusText += " | QR码检测器未初始化";
                statusStyle = "color: red; font-weight: bold;"; // 检测器问题用红色
            }

            // 更新状态标签
            if (m_statusLabel) {
                m_statusLabel->setText(statusText);
                m_statusLabel->setStyleSheet(statusStyle);
            }
            // QMessageBox::information(this, "拍照成功",
            //     QString("图片已成功保存到:\n%1").arg(finalPath));
            qDebug() << "Image successfully saved to:" << finalPath;
        } else {
            // 文件移动失败或文件不存在
            if (m_statusLabel) {
                m_statusLabel->setText("拍照异常");
                m_statusLabel->setStyleSheet("color: orange; font-weight: bold;");
            }
            QMessageBox::warning(this, "拍照异常", "抓图完成，但无法确认文件保存位置");
            qDebug() << "Capture completed but file location uncertain";
        }
    } else {
        // 抓图失败
        if (m_statusLabel) {
            m_statusLabel->setText("拍照失败");
            m_statusLabel->setStyleSheet("color: red; font-weight: bold;");
        }
        QMessageBox::warning(this, "拍照失败",
            QString("图片保存失败，错误码: %1").arg(result));
        qDebug() << "Image capture failed with error:" << result;
    }

    // 3秒后恢复状态标签
    // QTimer::singleShot(3000, this, [this]() {
    //     if (m_statusLabel) {
    //         if (m_isPreviewActive) {
    //             m_statusLabel->setText("预览中");
    //             m_statusLabel->setStyleSheet("color: green; font-weight: bold;");
    //         } else {
    //             m_statusLabel->setText("准备就绪");
    //             m_statusLabel->setStyleSheet("color: black;");
    //         }
    //     }
    // });
}

// 辅助方法：将字符串转换为安全的文件名
QString PreviewWindow::toSafeFileName(const QString& input)
{
    QString result = input;

    // Windows文件系统不允许的字符列表
    QStringList invalidChars = {":", "/", "\\", "*", "?", "\"", "<", ">", "|"};

    // 将所有无效字符替换为下划线
    for (const QString& invalidChar : invalidChars) {
        result.replace(invalidChar, "_");
    }

    // 移除开头和结尾的空格和点号（Windows不允许）
    result = result.trimmed();
    while (result.endsWith('.')) {
        result.chop(1);
    }

    // 如果结果为空，使用默认名称
    if (result.isEmpty()) {
        result = "unknown_device";
    }

    return result;
}
