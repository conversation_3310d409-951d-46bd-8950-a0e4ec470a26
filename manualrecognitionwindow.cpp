#include "manualrecognitionwindow.h"
#include "qrcodedetector.h"
#include <QDebug>

ManualRecognitionWindow::ManualRecognitionWindow(QRCodeDetector* qrDetector, QWidget *parent)
    : QD<PERSON><PERSON>(parent)
    , m_qrDetector(qrDetector)
    , m_pathLineEdit(nullptr)
    , m_browseButton(nullptr)
    , m_recognize<PERSON>utton(nullptr)
    , m_resultTextEdit(nullptr)
{
    setWindowTitle("手工识别");
    setMinimumSize(500, 400);
    setModal(true);  // 设置为模态对话框
    
    setupUI();
}

ManualRecognitionWindow::~ManualRecognitionWindow()
{
    qDebug() << "ManualRecognitionWindow destructor called";
}

void ManualRecognitionWindow::setupUI()
{
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    
    // 标题标签
    QLabel* titleLabel = new QLabel("手工识别二维码");
    titleLabel->setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;");
    titleLabel->setAlignment(Qt::AlignCenter);
    
    // 图片路径输入区域
    QLabel* pathLabel = new QLabel("输入本机图片地址:");
    pathLabel->setStyleSheet("font-weight: bold;");
    
    QHBoxLayout* pathLayout = new QHBoxLayout;
    m_pathLineEdit = new QLineEdit;
    m_pathLineEdit->setPlaceholderText("请输入图片文件的完整路径...");
    
    m_browseButton = new QPushButton("浏览...");
    m_browseButton->setMaximumWidth(80);
    
    pathLayout->addWidget(m_pathLineEdit);
    pathLayout->addWidget(m_browseButton);
    
    // 识别按钮
    m_recognizeButton = new QPushButton("识别");
    m_recognizeButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; } "
                                   "QPushButton:hover { background-color: #45a049; } "
                                   "QPushButton:disabled { background-color: #cccccc; color: #666666; }");
    m_recognizeButton->setMaximumWidth(100);
    
    // 结果显示区域
    QLabel* resultLabel = new QLabel("识别结果:");
    resultLabel->setStyleSheet("font-weight: bold; margin-top: 10px;");
    
    m_resultTextEdit = new QTextEdit;
    m_resultTextEdit->setPlaceholderText("识别结果将在这里显示...");
    m_resultTextEdit->setReadOnly(true);
    m_resultTextEdit->setMaximumHeight(150);
    
    // 布局组装
    mainLayout->addWidget(titleLabel);
    mainLayout->addSpacing(10);
    mainLayout->addWidget(pathLabel);
    mainLayout->addLayout(pathLayout);
    mainLayout->addSpacing(10);
    
    QHBoxLayout* buttonLayout = new QHBoxLayout;
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_recognizeButton);
    buttonLayout->addStretch();
    mainLayout->addLayout(buttonLayout);
    
    mainLayout->addSpacing(10);
    mainLayout->addWidget(resultLabel);
    mainLayout->addWidget(m_resultTextEdit);
    
    // 连接信号槽
    connect(m_browseButton, &QPushButton::clicked, this, &ManualRecognitionWindow::onBrowseButtonClicked);
    connect(m_recognizeButton, &QPushButton::clicked, this, &ManualRecognitionWindow::onRecognizeButtonClicked);
    
    qDebug() << "ManualRecognitionWindow UI setup completed";
}

void ManualRecognitionWindow::onBrowseButtonClicked()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "选择图片文件",
        "",
        "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.tiff);;所有文件 (*.*)");
    
    if (!fileName.isEmpty()) {
        m_pathLineEdit->setText(fileName);
        qDebug() << "Selected image file:" << fileName;
    }
}

void ManualRecognitionWindow::onRecognizeButtonClicked()
{
    if (!validateInput()) {
        return;
    }
    
    QString imagePath = m_pathLineEdit->text().trimmed();
    qDebug() << "Starting manual recognition for image:" << imagePath;
    
    // 清空之前的结果
    m_resultTextEdit->clear();
    m_resultTextEdit->append("正在识别，请稍候...");
    
    // 禁用识别按钮防止重复点击
    m_recognizeButton->setEnabled(false);
    
    if (!m_qrDetector) {
        m_resultTextEdit->clear();
        m_resultTextEdit->append("错误: 二维码检测器未初始化");
        m_recognizeButton->setEnabled(true);
        return;
    }
    
    if (!m_qrDetector->isInitialized()) {
        m_resultTextEdit->clear();
        m_resultTextEdit->append("错误: 二维码检测器未正确初始化");
        m_recognizeButton->setEnabled(true);
        return;
    }
    
    // 执行识别
    QStringList results = m_qrDetector->detectQRCodes(imagePath);
    
    // 显示结果
    m_resultTextEdit->clear();
    if (results.isEmpty()) {
        m_resultTextEdit->append("识别结果: 未检测到二维码");
        QString error = m_qrDetector->getLastError();
        if (!error.isEmpty()) {
            m_resultTextEdit->append(QString("错误信息: %1").arg(error));
        }
    } else {
        m_resultTextEdit->append(QString("识别成功! 检测到 %1 个二维码:").arg(results.size()));
        m_resultTextEdit->append("");
        for (int i = 0; i < results.size(); ++i) {
            m_resultTextEdit->append(QString("二维码 %1: %2").arg(i + 1).arg(results[i]));
        }
    }
    
    // 重新启用识别按钮
    m_recognizeButton->setEnabled(true);
    
    qDebug() << "Manual recognition completed, found" << results.size() << "QR codes";
}

bool ManualRecognitionWindow::validateInput()
{
    QString path = m_pathLineEdit->text().trimmed();
    
    if (path.isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入图片文件路径!");
        m_pathLineEdit->setFocus();
        return false;
    }
    
    QFileInfo fileInfo(path);
    if (!fileInfo.exists()) {
        QMessageBox::warning(this, "文件错误", "指定的文件不存在!");
        m_pathLineEdit->setFocus();
        return false;
    }
    
    if (!fileInfo.isFile()) {
        QMessageBox::warning(this, "文件错误", "指定的路径不是一个文件!");
        m_pathLineEdit->setFocus();
        return false;
    }
    
    return true;
}
