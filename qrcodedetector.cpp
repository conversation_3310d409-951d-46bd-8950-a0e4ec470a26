#include "qrcodedetector.h"
#include <QDir>
#include <QFileInfo>
#include <QCoreApplication>
#include <QFile>
#include <fstream>
#include <vector>

QRCodeDetector::QRCodeDetector()
    : m_initialized(false)
{
}

QRCodeDetector::~QRCodeDetector()
{
    // ZXing不需要手动释放内存
}

bool QRCodeDetector::initialize(const QString& modelPath)
{
    try {
        m_initialized = false;
        m_lastError.clear();

        // ZXing不需要模型文件，直接初始化
        qDebug() << "Initializing ZXing QR code detector...";

        // 对于ZXing，不需要额外的初始化步骤
        // 只需要确保ZXing库正常工作

        m_initialized = true;
        qDebug() << "ZXing QR code detector initialized successfully";

        // 如果提供了modelPath参数，给出提示信息
        if (!modelPath.isEmpty()) {
            qDebug() << "Note: ZXing does not require model files. Ignoring modelPath:" << modelPath;
        }

        return true;

    } catch (const cv::Exception& e) {
        setError(QString("OpenCV exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "OpenCV exception:" << e.what();
        return false;
    } catch (const std::exception& e) {
        setError(QString("Standard exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "Standard exception:" << e.what();
        return false;
    } catch (...) {
        setError("Unknown exception");
        qWarning() << "Unknown exception";
        return false;
    }
}

QStringList QRCodeDetector::detectQRCodes(const QString& imagePath)
{
    QStringList results;
    
    if (!m_initialized) {
        setError("Detector not initialized");
        return results;
    }
    
    if (!QFileInfo::exists(imagePath)) {
        setError(QString("Image file does not exist: %1").arg(imagePath));
        return results;
    }
    
    try {
        qDebug() << "Starting QR code detection, image:" << imagePath;
        // 使用安全的方法读取图片（支持中文路径）
        cv::Mat image = readImageSafely(imagePath);
        if (image.empty()) {
            setError(QString("Unable to read image: %1").arg(imagePath));
            return results;
        }

        qDebug() << "Image size:" << image.cols << "x" << image.rows;

        return detectQRCodes(image);
        
    } catch (const cv::Exception& e) {
        setError(QString("OpenCV exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "OpenCV exception occurred during QR code detection:" << e.what();
        return results;
    } catch (const std::exception& e) {
        setError(QString("Standard exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "Standard exception occurred during QR code detection:" << e.what();
        return results;
    } catch (...) {
        setError("Unknown exception occurred during QR code detection");
        qWarning() << "Unknown exception occurred during QR code detection";
        return results;
    }
}

QStringList QRCodeDetector::detectQRCodes(const cv::Mat& image)
{
    QStringList results;

    if (!m_initialized) {
        setError("Detector not initialized");
        return results;
    }

    if (image.empty()) {
        setError("Input image is empty");
        return results;
    }

    try {
        // 使用原始图片进行二维码检测
        qDebug() << "Detecting QR codes on original image";
        results = detectQRCodesInternal(image);

        // 如果识别成功，直接返回结果
        if (!results.isEmpty()) {
            qDebug() << "QR code detected on original image";
            m_lastError.clear();
            return results;
        }

        // 如果原始图片检测失败，尝试缩放策略
        qDebug() << "Original image detection failed, trying progressive scaling";

        // 先尝试2倍放大
        cv::Mat scaledImage2x;
        cv::resize(image, scaledImage2x, cv::Size(), 2.0, 2.0, cv::INTER_LINEAR);

        qDebug() << "Second attempt: 2x scaled image (" << scaledImage2x.cols << "x" << scaledImage2x.rows << ")";
        results = detectQRCodesInternal(scaledImage2x);

        if (!results.isEmpty()) {
            qDebug() << "QR code detected with 2x scaled image";
            m_lastError.clear();
            return results;
        }

        // 如果2倍放大仍然失败，再尝试4倍放大（仅对较小图像）
        int totalPixels = image.cols * image.rows;
        qDebug() << "Image pixels:" << totalPixels << "(" << image.cols << "x" << image.rows << ")";

        // 放宽限制：允许更大的图像进行4倍放大（8MP以下）
        if (totalPixels < 8000000) {
            qDebug() << "Second attempt failed, trying 4x scaled image";
            cv::Mat scaledImage4x;
            cv::resize(image, scaledImage4x, cv::Size(), 4.0, 4.0, cv::INTER_LINEAR);

            qDebug() << "Third attempt: 4x scaled image (" << scaledImage4x.cols << "x" << scaledImage4x.rows << ")";
            results = detectQRCodesInternal(scaledImage4x);

            if (!results.isEmpty()) {
                qDebug() << "QR code detected with 4x scaled image";
                m_lastError.clear();
                return results;
            }
        } else {
            qDebug() << "Image too large for 4x scaling, skipping. Pixels:" << totalPixels;
        }

        if (results.isEmpty()) {
            qDebug() << "No QR code detected in all attempts";
            setError("No QR codes detected after all attempts");
        }

        m_lastError.clear();
        return results;

    } catch (const cv::Exception& e) {
        setError(QString("OpenCV exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "OpenCV exception occurred during QR code detection:" << e.what();
        return results;
    } catch (const std::exception& e) {
        setError(QString("Standard exception: %1").arg(QString::fromStdString(e.what())));
        qWarning() << "Standard exception occurred during QR code detection:" << e.what();
        return results;
    } catch (...) {
        setError("Unknown exception occurred during QR code detection");
        qWarning() << "Unknown exception occurred during QR code detection";
        return results;
    }
}

void QRCodeDetector::setError(const QString& error)
{
    m_lastError = error;
    qWarning() << "QRCodeDetector error:" << error;
}

cv::Mat QRCodeDetector::readImageSafely(const QString& imagePath)
{
    cv::Mat image;

    try {
        // 使用QFile读取文件内容到内存
        QFile file(imagePath);
        if (!file.open(QIODevice::ReadOnly)) {
            qWarning() << "Failed to open file:" << imagePath;
            return image;
        }

        QByteArray fileData = file.readAll();
        file.close();

        if (fileData.isEmpty()) {
            qWarning() << "File is empty:" << imagePath;
            return image;
        }

        // 将QByteArray转换为std::vector<uchar>
        std::vector<uchar> buffer(fileData.begin(), fileData.end());

        // 使用cv::imdecode解码图像数据
        image = cv::imdecode(buffer, cv::IMREAD_COLOR);

        if (image.empty()) {
            qWarning() << "Failed to decode image data from file:" << imagePath;
        } else {
            qDebug() << "Successfully loaded image from:" << imagePath
                     << "Size:" << image.cols << "x" << image.rows;
        }

    } catch (const std::exception& e) {
        qWarning() << "Exception while reading image file:" << imagePath
                   << "Error:" << e.what();
    } catch (...) {
        qWarning() << "Unknown exception while reading image file:" << imagePath;
    }

    return image;
}

QStringList QRCodeDetector::detectQRCodesInternal(const cv::Mat& image)
{
    QStringList results;

    if (image.empty()) {
        return results;
    }

    try {
        // 使用ZXing进行二维码检测
        // 将OpenCV Mat转换为ZXing可以处理的格式
        ZXing::ImageView imageView(image.data, image.cols, image.rows, ZXing::ImageFormat::BGR);

        // 设置检测选项 - 支持多种条码格式
        ZXing::ReaderOptions options;
        // 设置支持的条码格式：二维码 + 常用一维码
        options.setFormats(ZXing::BarcodeFormat::Any);           // PDF417二维码
        // options.setTryHarder(true);
        options.setTryRotate(true);

        // 执行多条码检测
        auto barcodeResults = ZXing::ReadBarcodes(imageView, options);

        if (!barcodeResults.empty()) {
            qDebug() << "Found" << barcodeResults.size() << "barcodes in image";

            for (size_t i = 0; i < barcodeResults.size(); ++i) {
                const auto& result = barcodeResults[i];
                if (result.isValid()) {
                    QString barcodeText = QString::fromStdString(result.text());
                    QString formatName = QString::fromStdString(ZXing::ToString(result.format()));

                    if (!barcodeText.isEmpty()) {
                        results.append(barcodeText);
                        qDebug() << "Barcode" << (i + 1) << "detected";
                        qDebug() << "  Format:" << formatName;
                        qDebug() << "  Content:" << barcodeText;

                        // 打印位置信息
                        // auto position = result.position();
                        // if (!position.empty()) {
                        //     qDebug() << "  Position points count:" << position.size();
                        //     for (size_t j = 0; j < position.size(); ++j) {
                        //         qDebug() << "    Point" << j << ": (" << position[j].x << "," << position[j].y << ")";
                        //     }
                        // }
                    }
                }
            }
        } else {
            qDebug() << "No barcodes detected or failed to decode in this attempt";
        }

        qDebug() << "Detected" << results.size() << "barcodes in this attempt";

    } catch (const cv::Exception& e) {
        qWarning() << "OpenCV exception in detectQRCodesInternal:" << e.what();
    } catch (const std::exception& e) {
        qWarning() << "Standard exception in detectQRCodesInternal:" << e.what();
    } catch (...) {
        qWarning() << "Unknown exception in detectQRCodesInternal";
    }

    return results;
}
